# 连续文本阅读实验历史命令记录

## 2025-07-15 执行的命令
mkdir continuous_reading  # 创建连续阅读实验文件夹
cd continuous_reading
python text_materials.py  # 测试文本材料加载功能
conda activate et
python .\continuous_reading_experiment.py  # 第一次运行，发现DataManager缺少save_rating_data方法
conda activate et
python .\continuous_reading_experiment.py  # 修复后成功运行，测试评分功能
conda activate et
edf2asc.exe "data\20250715_201813_test_201812\test_201812.edf"  # 成功转换EDF文件
python continuous_reading_config.py  # 验证配置文件
python run_experiment.py  # 运行实验启动器
python continuous_reading_experiment.py  # 直接运行实验主程序

## 实验配置命令
# 修改reading_text.txt - 更新阅读文本
# 修改continuous_reading_config.py - 调整实验参数

## 数据分析命令
cd ..
cd analysis
python main.py --participant-id test_0715_1523 --data-type reading  # 分析连续阅读实验数据
python view_results.py --experiment-type reading  # 查看连续阅读实验结果

## 输出文件位置
# ../data/[timestamp]_[participant_id]/ - 原始数据
# ../analysis/output/ - 数据处理结果
# ../analysis/figures/ - 生成的图形

## 2025-07-15 修改评分确认方式
# 修改experiment_display.py中的get_rating方法，去除按回车确认的步骤
# 现在用户按数字键选择评分后直接进入下一个问题，无需再按回车确认

## 2025-07-15 修改句子分割逻辑支持多种标点符号
# 修改continuous_reading_config.py - 将sentence_delimiter改为sentence_delimiters列表，支持中英文句号、问号、感叹号
# 修改text_materials.py - 使用正则表达式分割句子，支持多种句末标点符号

## 2025-07-15 添加多句子同时显示配置
# 修改continuous_reading_config.py - 添加sentences_per_display和multi_sentence_separator配置参数
# 修改text_materials.py - 添加get_multiple_sentences和get_combined_sentences_text方法
# 修改continuous_reading_experiment.py - 修改主循环逻辑支持多句子显示，添加_present_sentences方法

## 2025-07-15 集成摄像头录像功能
# 修改continuous_reading_config.py - 添加CAMERA_CONFIG配置，包含摄像头录像相关参数
# 修改continuous_reading_experiment.py - 在EyeLinkManager初始化时传入摄像头参数，启用录像功能

## 2025-07-15 功能测试
python text_materials.py  # 测试文本材料加载和句子分割功能
python continuous_reading_config.py  # 验证配置文件
python test_modifications.py  # 运行所有修改功能的综合测试

## 2025-07-15 修改多句子显示时的评分逻辑
# 修改continuous_reading_experiment.py - 当一次显示多个句子时，只进行一轮评分而不是按句子数量进行多轮评分
# 添加_collect_ratings_for_group方法处理句子组评分

## 2025-07-15 调试录像文件保存位置
python test_camera_recording.py  # 测试录像文件保存位置和配置
# 录像文件保存位置: ../data/[timestamp]_[participant_id]/[participant_id]_facial_expression.avi

## 2025-07-15 修复EDF文件格式问题
# 修改continuous_reading_experiment.py - 添加start_recording和stop_recording调用
# 测试EDF文件转换: edf2asc.exe "data\[folder]\[file].edf"
python test_edf_generation.py  # 测试EDF文件生成和转换
# EDF文件格式正确，可以成功转换为ASC文件

## 2025-07-25 修改句子ID和内容映射保存
# 修改continuous_reading_experiment.py - 在_collect_ratings_for_group方法中添加句子内容保存
# 添加_save_sentence_mapping方法，保存完整的句子ID到内容的映射文件
# 现在每次实验都会生成sentence_mapping.json和sentence_mapping.txt文件

## 2025-08-20 添加Gemini出题功能
conda activate et; mkdir gemini  # 创建Gemini模块文件夹
conda activate et; cd gemini; python test_gemini.py  # 测试Gemini模块功能
# 创建了以下文件：
# - gemini/__init__.py - 模块初始化文件
# - gemini/gemini_config.py - Gemini配置文件，包含API设置、出题间隔、提示词等
# - gemini/gemini_client.py - Gemini API客户端，使用OpenAI兼容格式
# - gemini/question_generator.py - 题目生成器，管理文本收集和异步出题
# - gemini/quiz_display.py - 题目显示器，处理题目显示和用户交互
# - gemini/test_gemini.py - 模块测试文件
# 修改continuous_reading_config.py - 添加enable_gemini_quiz配置
# 修改continuous_reading_experiment.py - 集成Gemini出题功能到主实验流程
conda activate et; python test_gemini_integration.py  # 测试Gemini功能集成，所有测试通过
# 功能特性：
# - 每隔10段文本自动触发Gemini出题测试
# - 异步生成题目，减少等待时间
# - 4选项单选题，包含详细解释
# - 自动保存测试结果到实验数据目录
# - 支持模拟模式进行测试
# - 完整的错误处理和资源清理
