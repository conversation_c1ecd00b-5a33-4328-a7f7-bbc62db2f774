#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
题目显示器
处理题目显示和用户交互
"""

import time
import asyncio
from typing import Dict, List, Optional, Any, Tuple
try:
    from .gemini_config import DISPLAY_CONFIG, MESSAGE_CONFIG, DEBUG_CONFIG
except ImportError:
    from gemini_config import DISPLAY_CONFIG, MESSAGE_CONFIG, DEBUG_CONFIG

class QuizDisplay:
    """题目显示器"""
    
    def __init__(self, experiment_display):
        """
        初始化显示器
        
        Args:
            experiment_display: 实验显示对象
        """
        self.display = experiment_display
        
        # 配置
        self.option_keys = DISPLAY_CONFIG['option_keys']
        self.confirm_key = DISPLAY_CONFIG['confirm_key']
        self.skip_key = DISPLAY_CONFIG['skip_key']
        self.continue_key = DISPLAY_CONFIG['continue_key']
        self.question_timeout = DISPLAY_CONFIG['question_timeout']
        self.explanation_display_time = DISPLAY_CONFIG['explanation_display_time']
        
        # 消息配置
        self.messages = MESSAGE_CONFIG
        
    def show_loading_screen(self, message: str = None) -> bool:
        """
        显示加载界面
        
        Args:
            message: 自定义加载消息
            
        Returns:
            用户是否取消
        """
        if message is None:
            message = self.messages['loading_message']
            
        try:
            # 显示加载消息
            return self.display.show_question(
                question=message,
                duration=DISPLAY_CONFIG['loading_message_interval']
            )
        except Exception as e:
            print(f"显示加载界面失败: {e}")
            return True
            
    async def show_loading_with_status(self, generator) -> bool:
        """
        显示带状态更新的加载界面
        
        Args:
            generator: 题目生成器
            
        Returns:
            是否成功等待完成
        """
        try:
            while generator.is_generating:
                is_generating, status = generator.get_generation_status()
                
                if not self.show_loading_screen(status):
                    return False  # 用户取消
                    
                await asyncio.sleep(DISPLAY_CONFIG['loading_message_interval'])
                
            return True
            
        except Exception as e:
            print(f"显示加载状态失败: {e}")
            return False
            
    def run_quiz(self, questions_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行完整的测试
        
        Args:
            questions_data: 题目数据
            
        Returns:
            测试结果
        """
        if not questions_data or 'questions' not in questions_data:
            print("无效的题目数据")
            return {'success': False, 'error': 'invalid_data'}
            
        questions = questions_data['questions']
        if not questions:
            print("没有题目")
            return {'success': False, 'error': 'no_questions'}
            
        # 显示测试开始
        self.display.show_question(
            f"{self.messages['quiz_title']}\n\n共 {len(questions)} 道题目\n\n按回车键开始...",
            duration=None
        )
        
        # 运行每道题目
        results = []
        for i, question in enumerate(questions):
            result = self._run_single_question(question, i + 1, len(questions))
            if result is None:
                # 用户退出
                return {'success': False, 'error': 'user_quit'}
            results.append(result)
            
        # 显示测试完成
        correct_count = sum(1 for r in results if r['correct'])
        total_count = len(results)
        
        completion_message = f"{self.messages['quiz_complete']}\n\n"
        completion_message += f"正确率: {correct_count}/{total_count} ({correct_count/total_count*100:.1f}%)\n\n"
        completion_message += self.messages['continue_prompt']
        
        self.display.show_question(completion_message, duration=None)
        
        return {
            'success': True,
            'results': results,
            'correct_count': correct_count,
            'total_count': total_count,
            'accuracy': correct_count / total_count if total_count > 0 else 0
        }
        
    def _run_single_question(self, question: Dict[str, Any], question_num: int, total_questions: int) -> Optional[Dict[str, Any]]:
        """
        运行单个题目
        
        Args:
            question: 题目数据
            question_num: 题目编号
            total_questions: 总题目数
            
        Returns:
            题目结果，用户退出时返回None
        """
        try:
            # 构建题目显示文本
            question_text = self._format_question(question, question_num, total_questions)
            
            # 显示题目并获取用户选择
            user_answer = self._get_user_answer(question_text)
            
            if user_answer is None:
                return None  # 用户退出
                
            # 检查答案
            correct_answer = question['correct_answer']
            is_correct = user_answer == correct_answer
            
            # 显示结果和解释
            self._show_answer_feedback(question, user_answer, is_correct)
            
            return {
                'question_num': question_num,
                'question': question['question'],
                'user_answer': user_answer,
                'correct_answer': correct_answer,
                'correct': is_correct,
                'explanation': question.get('explanation', '')
            }
            
        except Exception as e:
            print(f"运行题目 {question_num} 时出错: {e}")
            return None
            
    def _format_question(self, question: Dict[str, Any], question_num: int, total_questions: int) -> str:
        """
        格式化题目显示文本
        
        Args:
            question: 题目数据
            question_num: 题目编号
            total_questions: 总题目数
            
        Returns:
            格式化的题目文本
        """
        text = f"{self.messages['question_prefix']} {question_num}/{total_questions}\n\n"
        text += f"{question['question']}\n\n"
        
        # 添加选项
        options = question['options']
        option_labels = self.messages['option_labels']
        
        for i, label in enumerate(option_labels):
            if label in options:
                text += f"{i+1}. {label}. {options[label]}\n"
                
        text += f"\n请按数字键 1-4 选择答案，然后按回车确认"
        text += f"\n按 ESC 键退出测试"
        
        return text
        
    def _get_user_answer(self, question_text: str) -> Optional[str]:
        """
        获取用户答案
        
        Args:
            question_text: 题目文本
            
        Returns:
            用户选择的答案（A/B/C/D），退出时返回None
        """
        try:
            # 使用现有的评分系统来获取用户选择
            # 将1-4映射到A-D
            option_labels = ['A', 'B', 'C', 'D']
            
            rating_value = self.display.get_rating(
                question=question_text,
                scale_range=(1, 4),
                labels=option_labels
            )
            
            if rating_value is None:
                return None  # 用户退出
                
            # 将数字转换为字母
            return option_labels[rating_value - 1]
            
        except Exception as e:
            print(f"获取用户答案失败: {e}")
            return None
            
    def _show_answer_feedback(self, question: Dict[str, Any], user_answer: str, is_correct: bool):
        """
        显示答案反馈
        
        Args:
            question: 题目数据
            user_answer: 用户答案
            is_correct: 是否正确
        """
        try:
            # 构建反馈文本
            feedback_text = ""
            
            if is_correct:
                feedback_text += f"✓ {self.messages['correct_feedback']}\n\n"
            else:
                feedback_text += f"✗ {self.messages['wrong_feedback']}\n"
                feedback_text += f"您的答案: {user_answer}\n"
                feedback_text += f"正确答案: {question['correct_answer']}\n\n"
                
            # 添加解释
            explanation = question.get('explanation', '')
            if explanation:
                feedback_text += f"解释:\n{explanation}\n\n"
                
            feedback_text += "按空格键继续..."
            
            # 显示反馈
            self.display.show_question(
                feedback_text,
                duration=None
            )
            
        except Exception as e:
            print(f"显示答案反馈失败: {e}")
            
    def show_error_message(self, error_type: str, details: str = "") -> bool:
        """
        显示错误消息
        
        Args:
            error_type: 错误类型
            details: 错误详情
            
        Returns:
            用户是否选择继续
        """
        try:
            error_message = self.messages.get(f"{error_type}_error", "发生未知错误")
            
            if details:
                error_message += f"\n\n详情: {details}"
                
            error_message += "\n\n按空格键继续阅读，按ESC键退出..."
            
            return self.display.show_question(error_message, duration=None)
            
        except Exception as e:
            print(f"显示错误消息失败: {e}")
            return True

# 便捷函数
def create_quiz_display(experiment_display) -> QuizDisplay:
    """
    创建题目显示器
    
    Args:
        experiment_display: 实验显示对象
        
    Returns:
        题目显示器实例
    """
    return QuizDisplay(experiment_display)
