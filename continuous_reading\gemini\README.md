# Gemini出题功能模块

## 功能概述

本模块为连续阅读实验添加了智能出题功能，使用Google Gemini API根据阅读文本自动生成4选项单选题。

## 主要特性

- **智能出题**: 根据文本内容自动生成针对知识点的选择题
- **异步处理**: 在后台提前生成题目，减少等待时间
- **灵活配置**: 可配置出题间隔、题目数量、提示词等
- **无缝集成**: 与现有连续阅读实验完美集成
- **结果保存**: 自动保存测试结果和详细数据

## 文件结构

```
gemini/
├── __init__.py              # 模块初始化
├── gemini_config.py         # 配置文件
├── gemini_client.py         # API客户端
├── question_generator.py    # 题目生成器
├── quiz_display.py          # 题目显示器
├── test_gemini.py          # 模块测试
└── README.md               # 说明文档
```

## 配置说明

### API配置 (gemini_config.py)

```python
GEMINI_CONFIG = {
    'api_key': os.getenv('GEMINI_API_KEY'),  # API密钥
    'model': 'gemini-2.0-flash',            # 模型名称
    'timeout': 30,                          # 请求超时
    'max_retries': 3,                       # 最大重试次数
}
```

### 出题配置

```python
QUIZ_CONFIG = {
    'quiz_interval': 10,          # 每隔10段进行一次测试
    'questions_per_quiz': 5,      # 每次测试题目数量
    'auto_question_count': True,  # 根据知识点自动确定题目数量
    'enable_async': True,         # 启用异步出题
}
```

### 显示配置

```python
DISPLAY_CONFIG = {
    'question_timeout': 30,       # 答题超时时间
    'option_keys': ['1', '2', '3', '4'],  # 选项按键
    'confirm_key': 'return',      # 确认按键
}
```

## 使用方法

### 1. 环境设置

设置Gemini API密钥：
```bash
# Windows
set GEMINI_API_KEY=your_api_key_here

# Linux/Mac
export GEMINI_API_KEY=your_api_key_here
```

### 2. 启用功能

在 `continuous_reading_config.py` 中设置：
```python
EXPERIMENT_CONFIG = {
    'enable_gemini_quiz': True,  # 启用Gemini出题功能
    # ... 其他配置
}
```

### 3. 运行实验

正常运行连续阅读实验即可，系统会自动在指定间隔触发出题测试。

## 工作流程

1. **文本收集**: 实验过程中自动收集已读文本段落
2. **触发条件**: 达到设定间隔（默认10段）时触发出题
3. **异步生成**: 在后台调用Gemini API生成题目
4. **题目显示**: 在屏幕上逐题显示，用户选择答案
5. **结果反馈**: 显示正确答案和详细解释
6. **数据保存**: 保存测试结果到实验数据目录

## 提示词模板

系统使用专业的提示词模板确保题目质量：

```
你是一个专业的阅读理解出题专家。请根据提供的文本内容，为每个重要知识点出一道4选项单选题。

要求：
1. 题目要准确反映文本中的关键信息和知识点
2. 选项设计要有一定难度，避免过于明显的错误选项
3. 正确答案必须在文本中有明确依据
4. 干扰项要合理，不能完全无关
5. 题目表述要清晰准确
6. 每道题都要提供详细的解释说明
```

## 数据输出

### 测试结果文件

每次测试会生成 `gemini_quiz_N.json` 文件，包含：
- 测试时间戳
- 题目和答案
- 用户选择
- 正确率统计
- 详细解释

### API日志

如果启用调试模式，会保存API请求和响应日志到 `gemini_api_logs/` 目录。

## 测试验证

运行模块测试：
```bash
cd gemini
python test_gemini.py
```

运行集成测试：
```bash
python test_gemini_integration.py
```

## 故障排除

### 常见问题

1. **API密钥错误**
   - 检查环境变量 `GEMINI_API_KEY` 是否正确设置
   - 确认API密钥有效且有足够配额

2. **网络连接问题**
   - 检查网络连接
   - 确认可以访问 `generativelanguage.googleapis.com`

3. **题目生成失败**
   - 检查文本长度是否足够（默认最小100字符）
   - 查看API响应日志排查问题

4. **显示问题**
   - 确认实验显示组件正常工作
   - 检查字体和编码设置

### 调试模式

启用调试模式进行问题排查：
```python
DEBUG_CONFIG = {
    'debug_mode': True,
    'verbose_logging': True,
    'save_api_responses': True,
    'simulate_api': True,  # 使用模拟API进行测试
}
```

## 性能优化

- 异步生成减少等待时间
- 智能缓存避免重复请求
- 可配置的超时和重试机制
- 优化的提示词减少token消耗

## 扩展功能

模块设计支持未来扩展：
- 支持其他AI模型
- 自定义题目类型
- 多语言支持
- 难度自适应调整
