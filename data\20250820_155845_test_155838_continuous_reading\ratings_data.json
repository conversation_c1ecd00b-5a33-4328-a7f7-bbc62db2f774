[{"sentence_group_ids": [1], "representative_sentence_id": 1, "sentence_count": 1, "timestamp": 1755676731.75989, "ratings": {"interest_rating": 2, "curiosity_rating": 2}, "sentence_texts": ["CS 285_ Lecture 20: Inverse Reinforcement Learning (Parts 1-4) ### Part 1 欢迎来到 CS285 的第 20 讲。"], "combined_text": "CS 285_ Lecture 20: Inverse Reinforcement Learning (Parts 1-4) ### Part 1 欢迎来到 CS285 的第 20 讲。"}, {"sentence_group_ids": [2, 3], "representative_sentence_id": 2, "sentence_count": 2, "timestamp": 1755676733.6938524, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["今天我们将讨论逆强化学习。", "到目前为止， 每次我们遇到强化学习问题时， 我们都会假设系统会为我们提供一个奖励函数。"], "combined_text": "今天我们将讨论逆强化学习。\n\n到目前为止， 每次我们遇到强化学习问题时， 我们都会假设系统会为我们提供一个奖励函数。"}, {"sentence_group_ids": [4, 5], "representative_sentence_id": 4, "sentence_count": 2, "timestamp": 1755676735.400546, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["通常， 如果你要使用这些强化学习算法， 你会手动编写一个奖励函数。", "但如果你的任务的奖励函数很难手动指定， 但你可以访问人类的数据， 或者一般来说， 某个专家成功地完成了这项任务， 你可以通过观察他们的行为来推导出他们的奖励函数， 然后用强化学习算法重新优化这个奖励函数。"], "combined_text": "通常， 如果你要使用这些强化学习算法， 你会手动编写一个奖励函数。\n\n但如果你的任务的奖励函数很难手动指定， 但你可以访问人类的数据， 或者一般来说， 某个专家成功地完成了这项任务， 你可以通过观察他们的行为来推导出他们的奖励函数， 然后用强化学习算法重新优化这个奖励函数。"}, {"sentence_group_ids": [6], "representative_sentence_id": 6, "sentence_count": 1, "timestamp": 1755676736.693228, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["今天我们要学习的是如何应用上次形式化为推理问题的近似最优模型来学习奖励函数， 而不是直接从已知的奖励中学习策略， 这就是所谓的逆强化学习问题。"], "combined_text": "今天我们要学习的是如何应用上次形式化为推理问题的近似最优模型来学习奖励函数， 而不是直接从已知的奖励中学习策略， 这就是所谓的逆强化学习问题。"}, {"sentence_group_ids": [7], "representative_sentence_id": 7, "sentence_count": 1, "timestamp": 1755676738.3875659, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["所以今天的目标是理解逆强化学习问题的定义， 理解如何使用行为概率模型来推导逆强化学习算法， 并理解一些我们可以在高维空间中使用的实用逆强化学习方法， 我们在深度强化学习中遇到的这类问题。"], "combined_text": "所以今天的目标是理解逆强化学习问题的定义， 理解如何使用行为概率模型来推导逆强化学习算法， 并理解一些我们可以在高维空间中使用的实用逆强化学习方法， 我们在深度强化学习中遇到的这类问题。"}, {"sentence_group_ids": [8, 9], "representative_sentence_id": 8, "sentence_count": 2, "timestamp": 1755676740.1446464, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["我在上一讲中提到的一件事是， 最优控制和强化学习可以作为人类行为的模型。", "事实上， 科学家们试图通过最优决策和理性的视角来研究人类运动、人类决策和人类行为， 这已经有很长的历史了。"], "combined_text": "我在上一讲中提到的一件事是， 最优控制和强化学习可以作为人类行为的模型。\n\n事实上， 科学家们试图通过最优决策和理性的视角来研究人类运动、人类决策和人类行为， 这已经有很长的历史了。"}, {"sentence_group_ids": [10], "representative_sentence_id": 10, "sentence_count": 1, "timestamp": 1755676744.5647857, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["事实上， 理性行为的定义之一是， 理性行为可以被定义为最大化一个定义明确的效用函数。"], "combined_text": "事实上， 理性行为的定义之一是， 理性行为可以被定义为最大化一个定义明确的效用函数。"}, {"sentence_group_ids": [11], "representative_sentence_id": 11, "sentence_count": 1, "timestamp": 1755676745.9742851, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["事实证明， 任何理性的决策策略， 例如， 如果你更喜欢a而不是b， 你更喜欢b而不是c， 那么你更喜欢a而不是c。"], "combined_text": "事实证明， 任何理性的决策策略， 例如， 如果你更喜欢a而不是b， 你更喜欢b而不是c， 那么你更喜欢a而不是c。"}, {"sentence_group_ids": [12, 13], "representative_sentence_id": 12, "sentence_count": 2, "timestamp": 1755676747.3179, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["任何在某种意义上是理性的策略都可以用一组定义明确的标量效用集来解释。", "而非理性的策略， 例如， 如果你更喜欢苹果而不是香蕉， 你更喜欢香蕉而不是橙子， 但你更喜欢橙子而不是苹果， 那是非理性的， 实际上无法用一组定义明确的标量效用集来解释。"], "combined_text": "任何在某种意义上是理性的策略都可以用一组定义明确的标量效用集来解释。\n\n而非理性的策略， 例如， 如果你更喜欢苹果而不是香蕉， 你更喜欢香蕉而不是橙子， 但你更喜欢橙子而不是苹果， 那是非理性的， 实际上无法用一组定义明确的标量效用集来解释。"}, {"sentence_group_ids": [14], "representative_sentence_id": 14, "sentence_count": 1, "timestamp": 1755676749.0552702, "ratings": {"interest_rating": 1, "curiosity_rating": 1}, "sentence_texts": ["所以， 如果我们想通过最优性的视角来解释人类运动、人类决策等等， 我们可以做的是， 写下描述最优决策的方程， 无论是确定性情况下的最优决策， 就像我们在最优控制课程中学到的那样， 还是随机情况下的最优决策。"], "combined_text": "所以， 如果我们想通过最优性的视角来解释人类运动、人类决策等等， 我们可以做的是， 写下描述最优决策的方程， 无论是确定性情况下的最优决策， 就像我们在最优控制课程中学到的那样， 还是随机情况下的最优决策。"}]