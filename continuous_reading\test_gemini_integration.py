#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试Gemini功能集成到主实验的功能
"""

import sys
import os
import asyncio

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_gemini_import():
    """测试Gemini模块导入"""
    print("=" * 50)
    print("测试Gemini模块导入")
    print("=" * 50)
    
    try:
        from gemini.question_generator import QuestionGenerator
        from gemini.quiz_display import QuizDisplay
        from gemini.gemini_config import validate_gemini_config
        
        print("✓ Gemini模块导入成功")
        
        # 验证配置
        result = validate_gemini_config()
        if result['valid']:
            print("✓ Gemini配置验证通过")
        else:
            print("✗ Gemini配置验证失败")
            for error in result['errors']:
                print(f"  ✗ {error}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Gemini模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False

def test_experiment_config():
    """测试实验配置"""
    print("\n" + "=" * 50)
    print("测试实验配置")
    print("=" * 50)
    
    try:
        from continuous_reading_config import EXPERIMENT_CONFIG
        
        enable_gemini = EXPERIMENT_CONFIG.get('enable_gemini_quiz', False)
        print(f"Gemini出题功能启用状态: {enable_gemini}")
        
        if enable_gemini:
            print("✓ Gemini出题功能已启用")
        else:
            print("⚠ Gemini出题功能未启用")
            
        return True
        
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False

def test_experiment_initialization():
    """测试实验初始化"""
    print("\n" + "=" * 50)
    print("测试实验初始化")
    print("=" * 50)
    
    try:
        # 启用模拟模式
        from gemini.gemini_config import DEBUG_CONFIG
        DEBUG_CONFIG['simulate_api'] = True
        
        from continuous_reading_experiment import ContinuousReadingExperiment
        
        # 创建实验对象
        experiment = ContinuousReadingExperiment(
            participant_id="test_gemini",
            use_eyelink=False,
            fullscreen=False
        )
        
        print(f"Gemini功能启用状态: {experiment.enable_gemini}")
        
        if experiment.enable_gemini:
            print("✓ 实验对象中Gemini功能已启用")
        else:
            print("⚠ 实验对象中Gemini功能未启用")
            
        # 清理
        experiment.cleanup()
        
        return True
        
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_question_generation():
    """测试题目生成功能"""
    print("\n" + "=" * 50)
    print("测试题目生成功能")
    print("=" * 50)
    
    try:
        # 启用模拟模式
        from gemini.gemini_config import DEBUG_CONFIG
        DEBUG_CONFIG['simulate_api'] = True
        
        from gemini.question_generator import QuestionGenerator
        
        generator = QuestionGenerator()
        
        # 添加测试文本
        test_texts = [
            f"这是第{i+1}段测试文本。包含重要的知识点和信息。"
            for i in range(10)
        ]
        
        should_quiz = False
        for text in test_texts:
            should_quiz = generator.add_text_segment(text)
            if should_quiz:
                print("✓ 触发了测试条件")
                break
                
        if should_quiz:
            # 生成题目
            questions = await generator.get_questions()
            
            if questions:
                question_count = len(questions.get('questions', []))
                print(f"✓ 成功生成 {question_count} 道题目")
                
                # 显示第一题
                if questions['questions']:
                    first_q = questions['questions'][0]
                    print(f"  第一题: {first_q['question']}")
                    print(f"  选项数量: {len(first_q['options'])}")
                    
                generator.complete_quiz()
                return True
            else:
                print("✗ 题目生成失败")
                return False
        else:
            print("✗ 未触发测试条件")
            return False
            
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        try:
            await generator.cleanup()
        except:
            pass

def run_all_tests():
    """运行所有测试"""
    print("开始Gemini集成测试")
    print("=" * 60)
    
    tests = [
        ("Gemini模块导入", test_gemini_import),
        ("实验配置", test_experiment_config),
        ("实验初始化", test_experiment_initialization),
        ("题目生成功能", test_question_generation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = asyncio.run(test_func())
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("集成测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有集成测试通过！")
        print("\nGemini出题功能已成功集成到连续阅读实验中。")
        print("\n使用说明：")
        print("1. 设置环境变量 GEMINI_API_KEY 为你的Gemini API密钥")
        print("2. 在 continuous_reading_config.py 中确保 enable_gemini_quiz = True")
        print("3. 运行实验时，每隔10段文本会自动触发Gemini出题测试")
        print("4. 题目会在后台异步生成，减少等待时间")
        print("5. 测试结果会保存在实验数据目录中")
    else:
        print("⚠ 部分集成测试失败，请检查问题")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
