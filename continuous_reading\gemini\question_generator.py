#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
题目生成器
管理文本收集、题目生成和缓存
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
try:
    from .gemini_client import GeminiClient
    from .gemini_config import QUIZ_CONFIG, DEBUG_CONFIG
except ImportError:
    from gemini_client import GeminiClient
    from gemini_config import QUIZ_CONFIG, DEBUG_CONFIG

class QuestionGenerator:
    """题目生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.client = GeminiClient()
        
        # 文本管理
        self.collected_texts: List[str] = []  # 已收集的文本段落
        self.current_segment = 0  # 当前段落计数
        self.quiz_count = 0  # 已进行的测试次数
        
        # 异步任务管理
        self.pending_task: Optional[asyncio.Task] = None
        self.generated_questions: Optional[Dict[str, Any]] = None
        self.is_generating = False
        
        # 配置
        self.quiz_interval = QUIZ_CONFIG['quiz_interval']
        self.min_text_length = QUIZ_CONFIG['min_text_length']
        self.system_prompt = QUIZ_CONFIG['system_prompt']
        self.user_prompt_template = QUIZ_CONFIG['user_prompt_template']
        
    def add_text_segment(self, text: str) -> bool:
        """
        添加文本段落
        
        Args:
            text: 文本内容
            
        Returns:
            是否需要进行测试
        """
        if not text or len(text.strip()) == 0:
            return False
            
        self.collected_texts.append(text.strip())
        self.current_segment += 1
        
        if DEBUG_CONFIG['verbose_logging']:
            print(f"添加文本段落 {self.current_segment}: {text[:50]}...")
            
        # 检查是否需要测试
        should_quiz = self._should_start_quiz()
        
        # 如果启用异步且需要测试，提前开始生成题目
        if should_quiz and QUIZ_CONFIG['enable_async'] and QUIZ_CONFIG['prefetch_questions']:
            self._start_async_generation()
            
        return should_quiz
        
    def _should_start_quiz(self) -> bool:
        """
        判断是否应该开始测试
        
        Returns:
            是否应该开始测试
        """
        # 检查段落数量
        if self.current_segment < self.quiz_interval:
            return False
            
        # 检查是否到达测试间隔
        if self.current_segment % self.quiz_interval != 0:
            return False
            
        # 检查文本长度
        total_text = self._get_combined_text()
        if len(total_text) < self.min_text_length:
            return False
            
        return True
        
    def _get_combined_text(self) -> str:
        """
        获取合并的文本内容
        
        Returns:
            合并后的文本
        """
        return '\n\n'.join(self.collected_texts)
        
    def _start_async_generation(self):
        """开始异步生成题目"""
        if self.is_generating or self.pending_task is not None:
            return
            
        if DEBUG_CONFIG['verbose_logging']:
            print("开始异步生成题目...")
            
        self.is_generating = True
        self.generated_questions = None
        
        # 创建异步任务
        combined_text = self._get_combined_text()
        self.pending_task = asyncio.create_task(
            self._generate_questions_async(combined_text)
        )
        
    async def _generate_questions_async(self, text: str) -> Optional[Dict[str, Any]]:
        """
        异步生成题目
        
        Args:
            text: 文本内容
            
        Returns:
            生成的题目数据
        """
        try:
            async with self.client as client:
                questions_data = await client.generate_questions(
                    text=text,
                    system_prompt=self.system_prompt,
                    user_prompt=self.user_prompt_template
                )
                
            self.generated_questions = questions_data
            self.is_generating = False
            
            if DEBUG_CONFIG['verbose_logging']:
                if questions_data:
                    question_count = len(questions_data.get('questions', []))
                    print(f"异步生成题目完成，共 {question_count} 道题")
                else:
                    print("异步生成题目失败")
                    
            return questions_data
            
        except Exception as e:
            print(f"异步生成题目出错: {e}")
            self.is_generating = False
            return None
            
    async def get_questions(self) -> Optional[Dict[str, Any]]:
        """
        获取题目（同步等待异步生成完成）
        
        Returns:
            题目数据，失败时返回None
        """
        # 如果有异步任务在进行，等待完成
        if self.pending_task is not None:
            try:
                if DEBUG_CONFIG['verbose_logging']:
                    print("等待异步题目生成完成...")
                    
                await self.pending_task
                self.pending_task = None
                
            except Exception as e:
                print(f"等待异步任务完成时出错: {e}")
                self.pending_task = None
                
        # 如果已经有生成的题目，直接返回
        if self.generated_questions is not None:
            questions = self.generated_questions
            self.generated_questions = None  # 清空缓存
            return questions
            
        # 如果没有异步生成，同步生成
        if DEBUG_CONFIG['verbose_logging']:
            print("同步生成题目...")
            
        combined_text = self._get_combined_text()
        async with self.client as client:
            return await client.generate_questions(
                text=combined_text,
                system_prompt=self.system_prompt,
                user_prompt=self.user_prompt_template
            )
            
    def is_questions_ready(self) -> bool:
        """
        检查题目是否已准备好
        
        Returns:
            题目是否已准备好
        """
        return (self.generated_questions is not None or 
                (self.pending_task is not None and self.pending_task.done()))
                
    def get_generation_status(self) -> Tuple[bool, str]:
        """
        获取生成状态
        
        Returns:
            (是否正在生成, 状态描述)
        """
        if self.generated_questions is not None:
            return False, "题目已准备就绪"
        elif self.is_generating:
            return True, "正在生成题目..."
        elif self.pending_task is not None:
            return True, "正在生成题目..."
        else:
            return False, "未开始生成"
            
    def complete_quiz(self):
        """完成一次测试，重置状态"""
        self.quiz_count += 1
        self.collected_texts.clear()
        self.current_segment = 0
        self.generated_questions = None
        
        # 取消未完成的异步任务
        if self.pending_task is not None and not self.pending_task.done():
            self.pending_task.cancel()
            self.pending_task = None
            
        self.is_generating = False
        
        if DEBUG_CONFIG['verbose_logging']:
            print(f"完成第 {self.quiz_count} 次测试，状态已重置")
            
    def get_stats(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            统计信息
        """
        return {
            'current_segment': self.current_segment,
            'quiz_count': self.quiz_count,
            'collected_texts_count': len(self.collected_texts),
            'total_text_length': len(self._get_combined_text()),
            'is_generating': self.is_generating,
            'questions_ready': self.is_questions_ready()
        }
        
    async def cleanup(self):
        """清理资源"""
        # 取消未完成的异步任务
        if self.pending_task is not None and not self.pending_task.done():
            self.pending_task.cancel()
            try:
                await self.pending_task
            except asyncio.CancelledError:
                pass
            self.pending_task = None
            
        # 关闭客户端
        await self.client.close()
        
        if DEBUG_CONFIG['verbose_logging']:
            print("题目生成器资源已清理")

# 便捷函数
def create_question_generator() -> QuestionGenerator:
    """创建题目生成器"""
    return QuestionGenerator()
