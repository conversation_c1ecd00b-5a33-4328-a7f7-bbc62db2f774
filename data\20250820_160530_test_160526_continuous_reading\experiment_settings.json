{"participant_id": "test_160526", "timestamp": "2025-08-20 16:05:32", "use_eyelink": false, "fullscreen": false, "max_sentences": 344, "total_available_sentences": 344, "text_file": "C:\\Users\\<USER>\\Desktop\\curiosity_pupil\\continuous_reading\\reading_material\\CS285\\LEC_20.txt", "text_statistics": {"total_sentences": 344, "total_characters": 16704, "total_words": 1172, "avg_sentence_length": 48.55813953488372, "avg_words_per_sentence": 3.4069767441860463, "min_sentence_length": 1, "max_sentence_length": 220, "text_file": "C:\\Users\\<USER>\\Desktop\\curiosity_pupil\\continuous_reading\\reading_material\\CS285\\LEC_20.txt"}, "TEXT_CONFIG": {"text_file": "reading_material\\CS285\\LEC_20.txt", "encoding": "utf-8", "sentence_delimiters": ["。", "？", "！", "?", "!", "."], "min_sentence_length": 1, "max_sentence_length": 500, "skip_empty_lines": true, "strip_whitespace": true}, "BASELINE_CONFIG": {"baseline_interval": 6, "baseline_duration": 3.0, "fixation_cross_size": 50, "fixation_cross_color": "white", "fixation_cross_width": 3, "baseline_background_color": "black"}, "TIMING_CONFIG": {"sentence_display": null, "sentence_min_display": 0, "rating_timeout": 5.0, "inter_sentence_interval": 0, "experiment_start_delay": 2.0, "experiment_end_delay": 1.0}, "RATING_CONFIG": {"curiosity_rating": {"question": "此刻, 你有多好奇，多想接着读下去?", "scale": [1, 5], "labels": {"1": "完全不好奇,不想读", "2": "不太想接着读", "3": "读不读都行", "4": "比较想接着读", "5": "极其好奇, 想立刻接着读"}, "instruction": "请按数字键1-5进行评分，然后按回车确认", "required": true}, "interest_rating": {"question": "刚才读的内容有多有趣, 有多少\"aha, 原来是这样\"的感觉?", "scale": [1, 5], "labels": {"1": "太无聊了", "2": "有点无聊", "3": "没感觉", "4": "有点意思哦", "5": "太有趣了, 太妙了"}, "instruction": "请按数字键1-5进行评分，然后按回车确认", "required": true}}, "RATING_ORDER": ["interest_rating", "curiosity_rating"], "DISPLAY_CONFIG": {"fullscreen": true, "screen_size": [1920, 1080], "background_color": "black", "text_color": "#202020", "font_size": 32, "font_name": "SimHei", "text_wrap_width": 1200, "line_spacing": 1.5, "text_posy": 250, "rating_font_size": 24, "instruction_font_size": 20}, "EYELINK_CONFIG": {"use_eyelink": true, "dummy_mode": false, "calibration_type": "HV9", "sampling_rate": 1000, "file_prefix": "reading", "pupil_size_mode": "AREA", "track_eyes": "BOTH"}, "CAMERA_CONFIG": {"enable_camera": true, "camera_index": 1, "video_width": 1920, "video_height": 1080, "video_fps": 30, "video_format": "avi", "video_codec": "MJPG", "video_filename_suffix": "facial_expression", "buffer_size": 10, "dummy_mode": false}, "DATA_CONFIG": {"base_data_dir": "data", "save_summary": true, "save_csv": true, "save_log": true, "auto_backup": false, "include_sentence_text": true, "max_sentence_text_length": 1000}, "EXPERIMENT_CONFIG": {"max_sentences": null, "start_from_sentence": 1, "allow_skip": false, "show_progress": true, "pause_between_ratings": 0, "confirm_quit": true, "auto_advance": false, "sentences_per_display": 8, "multi_sentence_separator": "\n\n", "use_character_count_mode": true, "target_character_count": 40, "listening_mode": false}, "LISTENING_CONFIG": {"voice": "zh-CN-YunxiNeural", "rate": "-0%", "prefix_text": "", "dot_size": 20, "dot_color": "black", "background_color": "black", "auto_advance_to_rating": true, "dot_position_x": 0, "dot_position_y": 350}, "DEBUG_CONFIG": {"debug_mode": false, "verbose_logging": true, "test_mode": false, "skip_calibration": false, "simulate_ratings": false, "max_test_sentences": 10}, "KEY_CONFIG": {"continue_key": "space", "quit_key": "escape", "rating_keys": ["1", "2", "3", "4", "5"], "confirm_key": "return", "skip_key": "s"}, "MESSAGE_CONFIG": {"use_english_messages": true, "sentence_prefix": "sentence", "rating_prefix": "rating", "baseline_prefix": "baseline", "include_sentence_number": true, "include_rating_value": true}}