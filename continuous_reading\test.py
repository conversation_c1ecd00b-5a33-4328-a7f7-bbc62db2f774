# from google import genai

# client = genai.Client(api_key="AIzaSyDgHKaJRSKK-ywZTBqv6Eu-C_uTxD3Hh5c")

# response = client.models.generate_content(
#     model="gemini-2.5-pro",
#     contents="1+99=？",
# )

# print(response.text)

from google import genai
from google.genai import types
from google.genai import errors

# ✅ 建议：不要把密钥写进代码；把 GEMINI_API_KEY 设为环境变量
client = genai.Client(api_key="AIzaSyDgHKaJRSKK-ywZTBqv6Eu-C_uTxD3Hh5c")

# ✅（可选）把 SDK 设成稳定 API 版本
# client = genai.Client(http_options=types.HttpOptions(api_version="v1"))

# ✅（可选）枚举可用模型，确认有 2.5-pro
# print([m.name for m in client.models.list() if "2.5" in m.name])

try:
    # 先用“零配置”规避已知 bug；若你之前传了 max_output_tokens，先去掉
    resp = client.models.generate_content(
        model="gemini-2.5-pro",
        contents="1+99=？",
        # 若仍空，可再加如下思考/输出配置再测：
        # config=types.GenerateContentConfig(
        #     thinking_config=types.ThinkingConfig(thinking_budget=512),  # 或 -1
        #     # max_output_tokens=64,  # 升级到最新 SDK 后再考虑打开
        # )
    )

    # 诊断信息（定位“有响应但没文本”的根因）
    if not getattr(resp, "candidates", None):
        print("No candidates. Full response:", resp)
    else:
        c0 = resp.candidates[0]
        print("finish_reason:", getattr(c0, "finish_reason", None))
        print("safety_ratings:", getattr(c0, "safety_ratings", None))
        print("usage:", getattr(resp, "usage_metadata", None))
        print("text:", resp.text)  # 期望输出 "100"
except errors.APIError as e:
    print("API error:", e.code, e.message)
