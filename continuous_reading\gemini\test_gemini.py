#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini模块测试文件
测试各个组件的功能
"""

import asyncio
import sys
import os

# 添加路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

import gemini_config
from gemini_config import validate_gemini_config, DEBUG_CONFIG
import gemini_client
from gemini_client import GeminiClient, generate_questions_async
import question_generator
from question_generator import QuestionGenerator
import quiz_display
from quiz_display import QuizDisplay

def test_config():
    """测试配置验证"""
    print("=" * 50)
    print("测试配置验证")
    print("=" * 50)
    
    result = validate_gemini_config()
    
    if result['valid']:
        print("✓ 配置验证通过")
        if result['warnings']:
            print("警告:")
            for warning in result['warnings']:
                print(f"  ⚠ {warning}")
    else:
        print("✗ 配置验证失败")
        for error in result['errors']:
            print(f"  ✗ {error}")
            
    return result['valid']

async def test_gemini_client():
    """测试Gemini客户端"""
    print("\n" + "=" * 50)
    print("测试Gemini客户端")
    print("=" * 50)
    
    # 启用模拟模式
    original_simulate = DEBUG_CONFIG['simulate_api']
    DEBUG_CONFIG['simulate_api'] = True
    
    try:
        test_text = "这是一段测试文本。它包含了一些基本信息。我们需要根据这些信息来生成题目。"
        system_prompt = "请根据文本生成题目。"
        user_prompt = "文本内容：{text}"
        
        print("发送测试请求...")
        result = await generate_questions_async(test_text, system_prompt, user_prompt)
        
        if result:
            print("✓ API调用成功")
            questions = result.get('questions', [])
            print(f"  生成题目数量: {len(questions)}")
            
            if questions:
                first_question = questions[0]
                print(f"  第一题: {first_question.get('question', 'N/A')[:50]}...")
                print(f"  选项数量: {len(first_question.get('options', {}))}")
                print(f"  正确答案: {first_question.get('correct_answer', 'N/A')}")
                
            return True
        else:
            print("✗ API调用失败")
            return False
            
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False
    finally:
        # 恢复原始设置
        DEBUG_CONFIG['simulate_api'] = original_simulate

async def test_question_generator():
    """测试题目生成器"""
    print("\n" + "=" * 50)
    print("测试题目生成器")
    print("=" * 50)
    
    # 启用模拟模式
    original_simulate = DEBUG_CONFIG['simulate_api']
    DEBUG_CONFIG['simulate_api'] = True
    
    try:
        generator = QuestionGenerator()
        
        # 测试文本添加
        print("测试文本添加...")
        # 生成足够的文本来触发测试（默认间隔是10）
        test_texts = [f"第{i+1}段测试文本。这里包含一些重要信息和知识点。" for i in range(12)]
        
        should_quiz = False
        for i, text in enumerate(test_texts):
            should_quiz = generator.add_text_segment(text)
            stats = generator.get_stats()
            print(f"  添加文本 {i+1}: 当前段落数={stats['current_segment']}, 应该测试={should_quiz}")
            
        if should_quiz:
            print("开始生成题目...")
            questions = await generator.get_questions()
            
            if questions:
                print("✓ 题目生成成功")
                question_count = len(questions.get('questions', []))
                print(f"  生成题目数量: {question_count}")
                
                # 完成测试
                generator.complete_quiz()
                final_stats = generator.get_stats()
                print(f"  测试完成后状态: {final_stats}")
                
                return True
            else:
                print("✗ 题目生成失败")
                return False
        else:
            print("✗ 未触发测试条件")
            return False
            
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False
    finally:
        # 恢复原始设置
        DEBUG_CONFIG['simulate_api'] = original_simulate
        # 清理资源
        try:
            await generator.cleanup()
        except:
            pass

def test_quiz_display():
    """测试题目显示器（模拟）"""
    print("\n" + "=" * 50)
    print("测试题目显示器")
    print("=" * 50)
    
    try:
        # 创建模拟显示对象
        class MockDisplay:
            def show_question(self, question, duration=None):
                print(f"[模拟显示] {question[:100]}...")
                return True
                
            def get_rating(self, question, scale_range, labels):
                print(f"[模拟评分] {question[:50]}...")
                print(f"  选项: {labels}")
                # 模拟用户选择第一个选项
                return 1
        
        mock_display = MockDisplay()
        quiz_display = QuizDisplay(mock_display)
        
        # 测试数据
        test_questions_data = {
            "questions": [
                {
                    "question": "这是第一道测试题目？",
                    "options": {
                        "A": "选项A",
                        "B": "选项B",
                        "C": "选项C",
                        "D": "选项D"
                    },
                    "correct_answer": "A",
                    "explanation": "这是第一题的解释。"
                },
                {
                    "question": "这是第二道测试题目？",
                    "options": {
                        "A": "选项A",
                        "B": "选项B",
                        "C": "选项C",
                        "D": "选项D"
                    },
                    "correct_answer": "B",
                    "explanation": "这是第二题的解释。"
                }
            ]
        }
        
        print("运行模拟测试...")
        result = quiz_display.run_quiz(test_questions_data)
        
        if result['success']:
            print("✓ 测试运行成功")
            print(f"  正确率: {result['accuracy']:.1%}")
            print(f"  题目数量: {result['total_count']}")
            return True
        else:
            print(f"✗ 测试运行失败: {result.get('error', 'unknown')}")
            return False
            
    except Exception as e:
        print(f"✗ 测试出错: {e}")
        return False

async def test_integration():
    """集成测试"""
    print("\n" + "=" * 50)
    print("集成测试")
    print("=" * 50)
    
    # 启用模拟模式
    original_simulate = DEBUG_CONFIG['simulate_api']
    DEBUG_CONFIG['simulate_api'] = True
    
    try:
        # 创建组件
        generator = QuestionGenerator()
        
        class MockDisplay:
            def show_question(self, question, duration=None):
                print(f"[显示] {question[:100]}...")
                return True
                
            def get_rating(self, question, scale_range, labels):
                print(f"[评分] {question[:50]}...")
                return 1
        
        mock_display = MockDisplay()
        quiz_display = QuizDisplay(mock_display)
        
        # 模拟完整流程
        print("模拟完整测试流程...")
        
        # 1. 添加文本直到触发测试
        test_texts = [f"测试文本段落 {i+1}。这里包含一些重要信息。" for i in range(10)]
        
        for text in test_texts:
            should_quiz = generator.add_text_segment(text)
            if should_quiz:
                print("触发测试条件")
                break
                
        # 2. 生成题目
        print("生成题目...")
        questions = await generator.get_questions()
        
        if not questions:
            print("✗ 题目生成失败")
            return False
            
        # 3. 运行测试
        print("运行测试...")
        result = quiz_display.run_quiz(questions)
        
        if result['success']:
            print("✓ 集成测试成功")
            
            # 4. 完成测试
            generator.complete_quiz()
            print("测试流程完成")
            return True
        else:
            print(f"✗ 测试运行失败: {result.get('error', 'unknown')}")
            return False
            
    except Exception as e:
        print(f"✗ 集成测试出错: {e}")
        return False
    finally:
        # 恢复原始设置
        DEBUG_CONFIG['simulate_api'] = original_simulate
        # 清理资源
        try:
            await generator.cleanup()
        except:
            pass

async def run_all_tests():
    """运行所有测试"""
    print("开始Gemini模块测试")
    print("=" * 60)
    
    tests = [
        ("配置验证", test_config),
        ("Gemini客户端", test_gemini_client),
        ("题目生成器", test_question_generator),
        ("题目显示器", test_quiz_display),
        ("集成测试", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"测试 {test_name} 出现异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠ 部分测试失败，请检查问题")
    
    return passed == total

if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
