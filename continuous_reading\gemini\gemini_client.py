#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini API客户端
使用OpenAI兼容格式调用Gemini API
"""

import json
import asyncio
import aiohttp
import time
from typing import Dict, List, Optional, Any
try:
    from .gemini_config import GEMINI_CONFIG, DEBUG_CONFIG
except ImportError:
    from gemini_config import GEMINI_CONFIG, DEBUG_CONFIG

class GeminiClient:
    """Gemini API客户端"""
    
    def __init__(self):
        """初始化客户端"""
        self.api_key = GEMINI_CONFIG['api_key']
        self.base_url = GEMINI_CONFIG['base_url']
        self.model = GEMINI_CONFIG['model']
        self.timeout = GEMINI_CONFIG['timeout']
        self.max_retries = GEMINI_CONFIG['max_retries']
        
        # 请求参数
        self.default_params = {
            'temperature': GEMINI_CONFIG['temperature'],
            'max_tokens': GEMINI_CONFIG['max_tokens'],
            'top_p': GEMINI_CONFIG['top_p']
        }
        
        # 会话管理
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        
    async def _ensure_session(self):
        """确保会话存在"""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
    async def close(self):
        """关闭会话"""
        if self.session and not self.session.closed:
            await self.session.close()
            
    async def generate_questions(self, text: str, system_prompt: str, user_prompt: str) -> Optional[Dict[str, Any]]:
        """
        生成题目
        
        Args:
            text: 文本内容
            system_prompt: 系统提示词
            user_prompt: 用户提示词模板
            
        Returns:
            生成的题目数据，失败时返回None
        """
        if DEBUG_CONFIG['simulate_api']:
            # 模拟API响应
            await asyncio.sleep(1)  # 模拟网络延迟
            return {
                "questions": DEBUG_CONFIG['test_questions']
            }
            
        try:
            await self._ensure_session()
            
            # 构建请求数据
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt.format(text=text)}
            ]
            
            request_data = {
                "model": self.model,
                "messages": messages,
                **self.default_params
            }
            
            # 发送请求
            response_data = await self._make_request(request_data)
            
            if response_data is None:
                return None
                
            # 解析响应
            return self._parse_response(response_data)
            
        except Exception as e:
            print(f"生成题目时出错: {e}")
            return None
            
    async def _make_request(self, request_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        发送API请求
        
        Args:
            request_data: 请求数据
            
        Returns:
            响应数据，失败时返回None
        """
        url = f"{self.base_url}chat/completions"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        for attempt in range(self.max_retries):
            try:
                if DEBUG_CONFIG['verbose_logging']:
                    print(f"发送API请求 (尝试 {attempt + 1}/{self.max_retries})")
                    
                async with self.session.post(url, json=request_data, headers=headers) as response:
                    if response.status == 200:
                        response_data = await response.json()
                        
                        if DEBUG_CONFIG['save_api_responses']:
                            self._save_api_response(request_data, response_data)
                            
                        return response_data
                    else:
                        error_text = await response.text()
                        print(f"API请求失败 (状态码: {response.status}): {error_text}")
                        
                        if response.status == 429:  # 速率限制
                            wait_time = 2 ** attempt
                            print(f"遇到速率限制，等待 {wait_time} 秒后重试")
                            await asyncio.sleep(wait_time)
                            continue
                        elif response.status >= 500:  # 服务器错误
                            wait_time = 1 + attempt
                            print(f"服务器错误，等待 {wait_time} 秒后重试")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            # 客户端错误，不重试
                            break
                            
            except asyncio.TimeoutError:
                print(f"请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 + attempt)
                    
            except Exception as e:
                print(f"请求出错 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                if attempt < self.max_retries - 1:
                    await asyncio.sleep(1 + attempt)
                    
        return None
        
    def _parse_response(self, response_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        解析API响应
        
        Args:
            response_data: API响应数据
            
        Returns:
            解析后的题目数据，失败时返回None
        """
        try:
            # 提取消息内容
            choices = response_data.get('choices', [])
            if not choices:
                print("API响应中没有choices")
                return None
                
            message = choices[0].get('message', {})
            content = message.get('content', '')
            
            if not content:
                print("API响应中没有内容")
                return None
                
            # 解析JSON内容
            # 尝试提取JSON部分（可能包含在代码块中）
            content = content.strip()
            if content.startswith('```json'):
                content = content[7:]
            if content.endswith('```'):
                content = content[:-3]
            content = content.strip()
            
            questions_data = json.loads(content)
            
            # 验证数据格式
            if not self._validate_questions_data(questions_data):
                print("题目数据格式验证失败")
                return None
                
            return questions_data
            
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            print(f"响应内容: {content[:500]}...")
            return None
        except Exception as e:
            print(f"解析响应时出错: {e}")
            return None
            
    def _validate_questions_data(self, data: Dict[str, Any]) -> bool:
        """
        验证题目数据格式
        
        Args:
            data: 题目数据
            
        Returns:
            是否有效
        """
        try:
            if 'questions' not in data:
                return False
                
            questions = data['questions']
            if not isinstance(questions, list) or len(questions) == 0:
                return False
                
            for question in questions:
                # 检查必需字段
                required_fields = ['question', 'options', 'correct_answer', 'explanation']
                for field in required_fields:
                    if field not in question:
                        return False
                        
                # 检查选项格式
                options = question['options']
                if not isinstance(options, dict):
                    return False
                    
                required_options = ['A', 'B', 'C', 'D']
                for option in required_options:
                    if option not in options:
                        return False
                        
                # 检查正确答案
                correct_answer = question['correct_answer']
                if correct_answer not in required_options:
                    return False
                    
            return True
            
        except Exception:
            return False
            
    def _save_api_response(self, request_data: Dict[str, Any], response_data: Dict[str, Any]):
        """
        保存API请求和响应（用于调试）
        
        Args:
            request_data: 请求数据
            response_data: 响应数据
        """
        try:
            import os
            from datetime import datetime
            
            # 创建保存目录
            save_dir = "gemini_api_logs"
            os.makedirs(save_dir, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"api_call_{timestamp}.json"
            filepath = os.path.join(save_dir, filename)
            
            # 保存数据
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "request": request_data,
                "response": response_data
            }
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存API日志失败: {e}")

# 同步包装器
def create_client() -> GeminiClient:
    """创建Gemini客户端"""
    return GeminiClient()

async def generate_questions_async(text: str, system_prompt: str, user_prompt: str) -> Optional[Dict[str, Any]]:
    """
    异步生成题目的便捷函数
    
    Args:
        text: 文本内容
        system_prompt: 系统提示词
        user_prompt: 用户提示词模板
        
    Returns:
        生成的题目数据，失败时返回None
    """
    async with GeminiClient() as client:
        return await client.generate_questions(text, system_prompt, user_prompt)
