#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Gemini出题功能配置文件
"""

import os

# ==================== Gemini API配置 ====================
GEMINI_CONFIG = {
    # API设置
    'api_key': os.getenv('GEMINI_API_KEY', 'AIzaSyBJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJJ'),  # 从环境变量获取
    'base_url': 'https://generativelanguage.googleapis.com/v1beta/openai/',
    'model': 'gemini-2.0-flash',
    'timeout': 30,  # 请求超时时间（秒）
    'max_retries': 3,  # 最大重试次数
    
    # 请求参数
    'temperature': 0.7,
    'max_tokens': 2000,
    'top_p': 0.9,
}

# ==================== 出题配置 ====================
QUIZ_CONFIG = {
    # 出题间隔设置
    'quiz_interval': 10,  # 每隔多少段进行一次测试
    'min_text_length': 100,  # 最小文本长度才开始出题
    
    # 题目设置
    'questions_per_quiz': 5,  # 每次测试的题目数量
    'auto_question_count': True,  # 是否根据文本知识点自动确定题目数量
    'max_questions': 10,  # 最大题目数量
    'min_questions': 3,   # 最小题目数量
    
    # 提示词模板
    'system_prompt': """你是一个专业的阅读理解出题专家。请根据提供的文本内容，为每个重要知识点出一道4选项单选题。

要求：
1. 题目要准确反映文本中的关键信息和知识点
2. 选项设计要有一定难度，避免过于明显的错误选项
3. 正确答案必须在文本中有明确依据
4. 干扰项要合理，不能完全无关
5. 题目表述要清晰准确
6. 每道题都要提供详细的解释说明

请严格按照以下JSON格式返回：
{
    "questions": [
        {
            "question": "题目内容",
            "options": {
                "A": "选项A",
                "B": "选项B", 
                "C": "选项C",
                "D": "选项D"
            },
            "correct_answer": "A",
            "explanation": "详细解释为什么这个答案是正确的，以及其他选项为什么错误"
        }
    ]
}""",
    
    'user_prompt_template': """请根据以下文本内容出题：

文本内容：
{text}

请为文本中的每个重要知识点出一道4选项单选题，题目数量应该与知识点数量相匹配。""",
    
    # 异步处理设置
    'enable_async': True,  # 是否启用异步出题
    'prefetch_questions': True,  # 是否提前获取题目
    'cache_questions': True,  # 是否缓存题目
}

# ==================== 显示配置 ====================
DISPLAY_CONFIG = {
    # 题目显示设置
    'question_font_size': 28,
    'option_font_size': 24,
    'explanation_font_size': 22,
    'title_font_size': 32,
    
    # 颜色设置
    'question_color': '#202020',
    'option_color': '#404040',
    'correct_color': '#008000',  # 正确答案颜色
    'wrong_color': '#800000',    # 错误答案颜色
    'explanation_color': '#606060',
    'background_color': 'white',
    
    # 布局设置
    'question_wrap_width': 1000,
    'option_spacing': 40,
    'explanation_wrap_width': 1200,
    'margin_top': 100,
    'margin_bottom': 100,
    
    # 交互设置
    'option_keys': ['1', '2', '3', '4'],  # 对应A、B、C、D
    'confirm_key': 'return',
    'skip_key': 'escape',
    'continue_key': 'space',
    
    # 时间设置
    'question_timeout': 30,  # 答题超时时间（秒）
    'explanation_display_time': 5,  # 解释显示时间（秒）
    'loading_message_interval': 2,  # 加载消息更新间隔（秒）
}

# ==================== 消息配置 ====================
MESSAGE_CONFIG = {
    # 界面消息
    'quiz_title': '阅读理解测试',
    'loading_message': '正在生成题目，请稍候...',
    'question_prefix': '题目',
    'option_labels': ['A', 'B', 'C', 'D'],
    'correct_feedback': '回答正确！',
    'wrong_feedback': '回答错误。',
    'timeout_message': '答题超时，显示正确答案。',
    'quiz_complete': '测试完成！',
    'continue_prompt': '按空格键继续阅读...',
    
    # 错误消息
    'api_error': 'API调用失败，跳过本次测试',
    'parse_error': '题目解析失败，跳过本次测试',
    'network_error': '网络连接失败，跳过本次测试',
}

# ==================== 调试配置 ====================
DEBUG_CONFIG = {
    'debug_mode': False,
    'verbose_logging': True,
    'save_api_responses': True,  # 是否保存API响应
    'simulate_api': False,  # 是否模拟API响应（测试用）
    'test_questions': [  # 测试用题目
        {
            "question": "这是一道测试题目？",
            "options": {
                "A": "选项A",
                "B": "选项B",
                "C": "选项C", 
                "D": "选项D"
            },
            "correct_answer": "A",
            "explanation": "这是测试解释。"
        }
    ]
}

# ==================== 辅助函数 ====================
def validate_gemini_config():
    """验证Gemini配置的有效性"""
    errors = []
    warnings = []
    
    # 检查API密钥
    if not GEMINI_CONFIG['api_key'] or GEMINI_CONFIG['api_key'].startswith('AIzaSy'):
        warnings.append("请设置有效的GEMINI_API_KEY环境变量")
    
    # 检查出题间隔
    if QUIZ_CONFIG['quiz_interval'] <= 0:
        errors.append("出题间隔必须大于0")
    
    # 检查题目数量设置
    if QUIZ_CONFIG['min_questions'] > QUIZ_CONFIG['max_questions']:
        errors.append("最小题目数量不能大于最大题目数量")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

if __name__ == "__main__":
    # 验证配置
    result = validate_gemini_config()
    if result['valid']:
        print("✓ Gemini配置验证通过")
        if result['warnings']:
            print("警告:")
            for warning in result['warnings']:
                print(f"  ⚠ {warning}")
    else:
        print("✗ Gemini配置验证失败")
        for error in result['errors']:
            print(f"  ✗ {error}")
